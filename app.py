from ursina import *
from ursina.prefabs.first_person_controller import FirstPersonController

app = Ursina()

# إنشاء بيئة المكعب الصغير
Entity(model='cube', scale=10, collider='box', texture='white_cube', color=color.gray)

# إعداد الكاميرا بمنظور الشخص الأول
player = FirstPersonController()
player.position = (0, 2, 0)

# توليد ووضع الأشكال الهندسية
shapes = []
for i in range(10):
    shape = Entity(model='sphere', collider='sphere', position=(random.uniform(-4, 4), random.uniform(1, 5), random.uniform(-4, 4)), color=color.random_color())
    shapes.append(shape)

# ضبط الإضاءة لخلق جو مظلم
ambient_light = AmbientLight(color=color.rgba(50, 50, 50, 255))
directional_light = DirectionalLight(color=color.rgba(50, 50, 50, 255), direction=(1, 1, 1))

def input(key):
    if key == 'left mouse down':
        hit_info = raycast(camera.world_position, camera.forward, distance=100)
        if hit_info.hit:
            if hit_info.entity in shapes:
                hit_info.entity.animate_position(hit_info.entity.position + hit_info.normal * 2, duration=0.1)
                hit_info.entity.animate_rotation(hit_info.entity.rotation + Vec3(random.uniform(-90, 90), random.uniform(-90, 90), random.uniform(-90, 90)), duration=0.1)


# إعدادات اللعبة
class GameSettings(Entity):
    def __init__(self):
        super().__init__()
        self.num_shapes = 10
        self.player_speed = 5
        self.ambient_light_intensity = 50

    def update_settings(self):
        # تحديث عدد الأشكال
        for s in shapes:
            destroy(s)
        shapes.clear()
        for i in range(self.num_shapes):
            shape = Entity(model='sphere'
                           , collider='sphere'
                           , position=(random.uniform(-4, 4), random.uniform(1, 5), random.uniform(-4, 4))
                           , color=color.random_color())
            shapes.append(shape)

        # تحديث سرعة اللاعب
        player.speed = self.player_speed

        # تحديث الإضاءة المحيطة
        ambient_light.color = color.rgba(self.ambient_light_intensity, self.ambient_light_intensity, self.ambient_light_intensity, 255)

settings = GameSettings()

# واجهة المستخدم للإعدادات
class SettingsMenu(Entity):
    def __init__(self):
        super().__init__(parent=camera.ui)
        self.background = Entity(parent=self, model='quad'
                                 , scale=(0.5, 0.8)
                                 , origin=(-0.5, 0.5)
                                 , position=(-0.25, 0.4)
                                 , color=color.black66)

        self.num_shapes_slider = Slider(min=1, max=50, default=settings.num_shapes, step=1, text='عدد الأشكال'
                                        , parent=self.background
                                        , y=0.4
                                        , x=-0.4)
        self.num_shapes_slider.on_value_changed = self.update_num_shapes

        self.player_speed_slider = Slider(min=1, max=20, default=settings.player_speed, step=1, text='سرعة اللاعب'
                                         , parent=self.background
                                         , y=0.3
                                         , x=-0.4)
        self.player_speed_slider.on_value_changed = self.update_player_speed

        self.light_intensity_slider = Slider(min=0, max=255, default=settings.ambient_light_intensity, step=1, text='شدة الإضاءة'
                                            , parent=self.background
                                            , y=0.2
                                            , x=-0.4)
        self.light_intensity_slider.on_value_changed = self.update_light_intensity

        self.visible = False

    def update_num_shapes(self):
        settings.num_shapes = int(self.num_shapes_slider.value)
        settings.update_settings()

    def update_player_speed(self):
        settings.player_speed = self.player_speed_slider.value
        player.speed = settings.player_speed

    def update_light_intensity(self):
        settings.ambient_light_intensity = int(self.light_intensity_slider.value)
        ambient_light.color = color.rgba(settings.ambient_light_intensity, settings.ambient_light_intensity, settings.ambient_light_intensity, 255)

settings_menu = SettingsMenu()

def update():
    if held_keys['escape']:
        application.quit()

    if held_keys['tab']:
        settings_menu.visible = not settings_menu.visible
        mouse.locked = not settings_menu.visible

app.run()

