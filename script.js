// JavaScript for login form functionality
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // Basic validation
            if (username.trim() === '' || password.trim() === '') {
                alert('Please fill in all fields');
                return;
            }
            
            // Simple login logic (you can modify this as needed)
            if (username === 'admin' && password === 'password') {
                alert('Login successful!');
                // Redirect or perform other actions here
            } else {
                alert('Invalid username or password');
            }
        });
    }
});
